annotated-types==0.7.0
anthropic==0.54.0
anyio==4.9.0
argcomplete==3.6.2
asgiref==3.8.1
bcrypt==4.3.0
boto3==1.38.35
botocore==1.38.35
cachetools==5.5.2
certifi==2025.4.26
cffi==1.17.1
charset-normalizer==3.4.2
click==8.2.1
cohere==5.15.0
colorama==0.4.6
cryptography==45.0.3
distro==1.9.0
Django==5.2.1
django-cors-headers==4.7.0
django-pandas==0.6.7
djangorestframework==3.16.0
et_xmlfile==2.0.0
eval_type_backport==0.2.2
Faker==37.3.0
fasta2a==0.2.16
fastavro==1.11.1
filelock==3.18.0
fsspec==2025.5.1
fuzzywuzzy==0.18.0
google-auth==2.40.3
google-auth-oauthlib==1.2.2
google-genai==1.20.0
griffe==1.7.3
groq==0.27.0
h11==0.16.0
hf-xet==1.1.3
httpcore==1.0.9
httpx==0.28.1
httpx-sse==0.4.0
huggingface-hub==0.33.0
idna==3.10
importlib_metadata==8.7.0
jiter==0.10.0
jmespath==1.0.1
logfire-api==3.18.0
markdown-it-py==3.0.0
mcp==1.9.4
mdurl==0.1.2
mistralai==1.8.2
numpy==2.3.0
oauthlib==3.2.2
openai==1.86.0
openpyxl==3.1.5
opentelemetry-api==1.34.1
packaging==25.0
pandas==2.3.0
paramiko==3.5.1
prompt_toolkit==3.0.51
psycopg2-binary==2.9.10
pyasn1==0.6.1
pyasn1_modules==0.4.2
pycparser==2.22
pydantic==2.11.5
pydantic-ai==0.2.16
pydantic-ai-slim==0.2.16
pydantic-evals==0.2.16
pydantic-graph==0.2.16
pydantic-settings==2.9.1
pydantic_core==2.33.2
Pygments==2.19.1
PyNaCl==1.5.0
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
python-json-logger==3.3.0
python-multipart==0.0.20
pytz==2025.2
PyYAML==6.0.2
requests==2.32.3
requests-oauthlib==2.0.0
rich==14.0.0
rsa==4.9.1
s3transfer==0.13.0
six==1.17.0
sniffio==1.3.1
sqlparse==0.5.3
sse-starlette==2.3.6
sshtunnel==0.4.0
starlette==0.47.0
tokenizers==0.21.1
tqdm==4.67.1
types-requests==2.32.4.20250611
typing-inspection==0.4.1
typing_extensions==4.14.0
tzdata==2025.2
urllib3==2.4.0
uvicorn==0.34.3
wcwidth==0.2.13
websockets==15.0.1
xlrd==2.0.2
zipp==3.23.0
