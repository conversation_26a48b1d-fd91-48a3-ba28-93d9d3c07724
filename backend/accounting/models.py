from django.db import models

from utils.models import BaseModel, DEFAULT_CHAR_FIELD_MAX_LENGTH


class Entity(BaseModel):
    name = models.Char<PERSON>ield(max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH, unique=True)
    address = models.CharField(
        max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH, blank=True, null=True
    )
    phone_number = models.CharField(
        max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH, blank=True, null=True
    )
    currency = models.Char<PERSON>ield(
        max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH,
        help_text="currency of the entity, e.g. 'USD', 'EUR', etc.",
        blank=True,
        null=True,
    )

    def __str__(self):
        return self.name


# Create your models here.
class ChartofAccount(BaseModel):
    entity = models.ForeignKey(Entity, on_delete=models.CASCADE)
    account_id = models.CharField(max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH)
    account_code = models.Char<PERSON>ield(
        max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH, null=True, blank=True
    )
    account_name = models.CharField(
        max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH, blank=True, null=True
    )
    account_type = models.CharField(
        max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH,
        db_index=True,
        help_text="Asset, Liability, Equity, Income, Expense",
    )
    parent = models.ForeignKey(
        "self", on_delete=models.CASCADE, null=True, blank=True, related_name="children"
    )
    remark = models.CharField(
        max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH, blank=True, null=True
    )
    clinic_code = models.CharField(
        max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH, blank=True, null=True
    )
    clinic_name = models.CharField(
        max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH, blank=True, null=True
    )
    cash_account = models.BooleanField(
        default=False
    )  # true -> cash account, false -> non-cash account (Finanse)
    is_postable = models.BooleanField(
        default=False
    )  # true -> active account, false -> title account (Postable)
    frozen = models.BooleanField(
        default=False
    )  # true -> account on hold, false -> active account (Frozen)
    fixed = models.BooleanField(
        default=False
    )  # true -> main account, false -> variable account (Fixed)
    level = models.IntegerField(default=0)
    source = models.CharField(
        max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH,
        help_text="source of the account, e.g. 'Xero', 'SAP', etc.",
        blank=True,
        null=True,
    )
    # E=Expenditure, I=Sales, N=Other
    account_type_db = models.CharField(
        max_length=1,
        choices=[
            ("E", "Expenditure"),
            ("I", "Sales"),
            ("N", "Other"),
        ],
        default="N",
    )

    def __str__(self):
        return f"{self.account_code} - {self.account_name}"

    @property
    def is_leaf(self):
        return self.children.count() == 0

    class Meta:
        indexes = [
            models.Index(fields=["entity", "account_id"]),
        ]


class JournalEntry(BaseModel):
    entity = models.ForeignKey(Entity, on_delete=models.CASCADE, db_index=True)
    transaction_id = models.CharField(
        max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH,
        help_text="id from the source system",
        db_index=True,
    )
    transaction_status = models.CharField(
        max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH,
        choices=[
            ("opened", "Opened"),
            ("closed", "Closed"),
        ],
        default="opened",
        help_text="status of the transaction, e.g. 'opened', 'closed', etc.",
    )
    transaction_type = models.CharField(
        max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH,
        help_text="type of transaction, e.g. 'invoice', 'payment', etc.",
        null=True,
        blank=True,
    )
    base_reference = models.CharField(
        max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH,
        blank=True,
        null=True,
        help_text="reference number from the source system, e.g. invoice number, etc.",
    )
    transaction_date = models.DateField(help_text="transaction date of the transaction")
    memo = models.CharField(
        max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH,
        blank=True,
        null=True,
        help_text="memo of the transaction",
    )
    reference_1 = models.CharField(
        max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH,
        blank=True,
        null=True,
        help_text="reference 1 of the transaction",
    )
    reference_2 = models.CharField(
        max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH,
        blank=True,
        null=True,
        help_text="reference 2 of the transaction",
    )
    transaction_amount = models.DecimalField(
        max_digits=19,
        decimal_places=2,
        default=0.00,
        help_text="transaction amount of the transaction",
    )
    transaction_currency = models.CharField(
        max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH,
        help_text="currency of the transaction",
    )
    reporting_currency = models.CharField(
        max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH,
        help_text="reporting currency of the transaction",
        blank=True,
        null=True,
    )
    reporting_amount = models.DecimalField(
        max_digits=19,
        decimal_places=2,
        default=0.00,
        help_text="reporting amount of the transaction",
    )
    source = models.CharField(
        max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH,
        help_text="source of the transaction, e.g. 'Xero', 'SAP', etc.",
        blank=True,
        null=True,
    )

    class Meta:
        indexes = [
            models.Index(fields=["entity", "transaction_id"]),
        ]


class JournalEntryTransaction(BaseModel):
    entity = models.ForeignKey(Entity, on_delete=models.CASCADE, db_index=True)
    journal_entry = models.ForeignKey(
        JournalEntry, on_delete=models.CASCADE, related_name="transactions"
    )
    transaction_id = models.CharField(
        max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH,
        blank=True,
        null=True,
        help_text="id from the source system",
        db_index=True,
    )
    line_id = models.CharField(
        max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH,
        blank=True,
        null=True,
        help_text="line id from the source system",
        db_index=True,
    )
    chart_of_account = models.ForeignKey(
        ChartofAccount, on_delete=models.PROTECT, related_name="journal_entries"
    )
    transaction_date = models.DateField(db_index=True)
    memo = models.CharField(max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH)
    transaction_currency = models.CharField(
        max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH, blank=True, null=True
    )  # currency of the transaction, e.g. 'USD', 'EUR', etc.
    transaction_debit_amount = models.DecimalField(
        max_digits=19, decimal_places=2, default=0.00
    )  # debit amount of the transaction
    transaction_credit_amount = models.DecimalField(
        max_digits=19, decimal_places=2, default=0.00
    )  # credit amount of the transaction
    reporting_currency = models.CharField(
        max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH, blank=True, null=True
    )  # reporting currency of the transaction, e.g. 'USD', 'EUR', etc.
    reporting_debit_amount = models.DecimalField(
        max_digits=19, decimal_places=2, default=0.00
    )  # reporting debit amount of the transaction
    reporting_credit_amount = models.DecimalField(
        max_digits=19, decimal_places=2, default=0.00
    )  # reporting credit amount of the transaction
    account_code = models.CharField(
        max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH,
        blank=True,
        null=True,
        help_text="code of the account, e.g. '1000', '2000', etc.",
    )  # code of the account, e.g. '1000', '2000', etc.
    offset_account_code = models.CharField(
        max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH,
        blank=True,
        null=True,
        help_text="code of the offset account, e.g. '1000', '2000', etc.",
    )  # code of the offset account, e.g. '1000', '2000', etc.

    reference_1 = models.CharField(
        max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH,
        blank=True,
        null=True,
        help_text="reference 1 of the transaction",
    )
    reference_2 = models.CharField(
        max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH,
        blank=True,
        null=True,
        help_text="reference 2 of the transaction",
    )
    base_reference = models.CharField(
        max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH,
        blank=True,
        null=True,
        help_text="reference number from the source system, e.g. invoice number, etc.",
    )
    is_adjustment = models.BooleanField(
        default=False, help_text="true if the transaction is an adjustment"
    )  # true if the transaction is an adjustment

    class Meta:
        indexes = [
            models.Index(fields=["entity", "transaction_id", "line_id"]),
        ]

    def __str__(self):
        return f"{self.transaction_date} - {self.memo}"


class Clinic(BaseModel):
    name = models.CharField(max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH)
    code = models.CharField(max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH, unique=True)
    segment = models.CharField(max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH)
    doctor = models.CharField(
        max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH, blank=True, null=True
    )

    def __str__(self):
        return self.name


class Employee(BaseModel):
    code = models.CharField(max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH, unique=True)
    clinic_name = models.CharField(max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH)
    department = models.CharField(max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH)
    category = models.CharField(max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH)
    occupation = models.CharField(max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH)
    joined_date = models.DateField()
    resignation_date = models.DateField(blank=True, null=True)
    resignation_reason = models.CharField(
        max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH, blank=True, null=True
    )

    def __str__(self):
        return self.code
