import os
import pandas as pd
from django.core.management.base import BaseCommand, CommandError
from accounting.models import Employee

# Usage:
# python manage.py populate_employee_details --excel-file "file.xls" --sheet-name Y2024


class Command(BaseCommand):
    help = "Populate or update Employee records from an Excel (.xlsx) file."

    def add_arguments(self, parser):
        parser.add_argument(
            "--excel-file",
            type=str,
            required=True,
            help="Path to the Excel file (.xlsx) containing employee details.",
        )
        parser.add_argument(
            "--sheet-name",
            nargs="+",
            default=None,
            help="One or more specific sheet names to process. If omitted, all sheets are processed.",
        )

    def handle(self, *args, **options):
        excel_file = options["excel_file"]
        sheet_names = options.get("sheet_name")

        if not os.path.exists(excel_file):
            raise CommandError(f"The file '{excel_file}' does not exist.")

        try:
            # Load Excel data
            df_dict = pd.read_excel(excel_file, sheet_name=sheet_names or None)

            # Normalize single sheet return
            if isinstance(df_dict, pd.DataFrame):
                df_dict = {sheet_names[0]: df_dict}

            # Map original Excel column names to model field names
            rename_map = {
                "EMPLOYEE CODE": "code",
                "COST CENTRE DESC": "clinic_name",
                "DEPARTMENT DESC": "department",
                "CATEGORY DESC": "category",
                "OCCUPATION DESC": "occupation",
                "HIRED DATE": "joined_date",
                "RESIGNATION DATE": "resignation_date",
                "RESIGNATION REASON": "resignation_reason",
            }

            required_columns = list(rename_map.keys())

            for sheet, df in df_dict.items():
                self.stdout.write(self.style.SUCCESS(f"\nProcessing sheet: {sheet}"))

                # Check for required columns
                missing = [col for col in required_columns if col not in df.columns]
                if missing:
                    self.stdout.write(
                        self.style.ERROR(
                            f"Missing columns in sheet '{sheet}': {', '.join(missing)}"
                        )
                    )
                    continue

                # Rename and clean up data
                df = df.rename(columns=rename_map)

                # Parse date fields
                df["joined_date"] = pd.to_datetime(df["joined_date"], errors="coerce")
                df["resignation_date"] = pd.to_datetime(
                    df["resignation_date"], errors="coerce"
                )

                # Clean non-date string fields
                for col in df.columns:
                    if col not in ["joined_date", "resignation_date"]:
                        df[col] = df[col].fillna("").astype(str).str.strip()

                selected_columns = list(rename_map.values())
                records = df[selected_columns].to_dict(orient="records")

                for record in records:
                    if not record["code"]:
                        self.stdout.write(
                            self.style.WARNING(
                                "Skipping record with empty employee code."
                            )
                        )
                        continue

                    # Ensure proper Python date or None
                    if pd.notnull(record["joined_date"]):
                        record["joined_date"] = record["joined_date"].date()
                    else:
                        record["joined_date"] = None

                    if pd.notnull(record["resignation_date"]):
                        record["resignation_date"] = record["resignation_date"].date()
                    else:
                        record["resignation_date"] = None

                    employee, created = Employee.objects.update_or_create(
                        code=record["code"],
                        defaults={
                            "clinic_name": record["clinic_name"],
                            "department": record["department"],
                            "category": record["category"],
                            "occupation": record["occupation"],
                            "joined_date": record["joined_date"],
                            "resignation_date": record["resignation_date"],
                            "resignation_reason": record["resignation_reason"] or None,
                        },
                    )
                    if created:
                        self.stdout.write(
                            self.style.SUCCESS(f"Created employee: {employee.code}")
                        )
                    else:
                        self.stdout.write(
                            self.style.NOTICE(f"Updated employee: {employee.code}")
                        )

        except Exception as e:
            raise CommandError(f"Error processing Excel file: {str(e)}")
