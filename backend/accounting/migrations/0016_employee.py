# Generated by Django 5.2.1 on 2025-07-16 02:24

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounting', '0015_clinic'),
    ]

    operations = [
        migrations.CreateModel(
            name='Employee',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('code', models.Char<PERSON>ield(max_length=1024, unique=True)),
                ('clinic_name', models.<PERSON>r<PERSON><PERSON>(max_length=1024)),
                ('department', models.Char<PERSON><PERSON>(max_length=1024)),
                ('category', models.Char<PERSON><PERSON>(max_length=1024)),
                ('occupation', models.<PERSON>r<PERSON><PERSON>(max_length=1024)),
                ('joined_date', models.DateField()),
                ('resignation_date', models.Date<PERSON><PERSON>(blank=True, null=True)),
                ('resignation_reason', models.<PERSON><PERSON><PERSON><PERSON>(blank=True, max_length=1024, null=True)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
