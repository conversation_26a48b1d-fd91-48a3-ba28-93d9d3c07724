"use client"

import useSWRImmutable from "swr/immutable"

import { CustomResponse } from "@/types/response"
import {
  OperationsAndHROverviewData,
  OrgChartData,
  ProfitAndLossRevenueBreakdownData,
  ProfitAndLossRevenueData,
  StackedBarData,
} from "@/types/summary"
import { createFetcher } from "@/services/fetcher"

// Filter types
export interface SummaryFilters {
  currency?: string
  year?: number
  last_n_years?: number
  segment?: string[]
  sub_segment?: string[]
  clinic?: string[]
  doctor?: string[]
  service?: string[]
}

// Helper function to build query string from filters
const buildQueryString = (filters: SummaryFilters): string => {
  const params = new URLSearchParams()

  // Add basic filters
  if (filters.currency) params.append("currency", filters.currency)
  if (filters.year) params.append("year", filters.year.toString())
  if (filters.last_n_years)
    params.append("last_n_years", filters.last_n_years.toString())

  // Add array filters
  if (filters.segment?.length) {
    filters.segment.forEach((value) => params.append("segment", value))
  }
  if (filters.sub_segment?.length) {
    filters.sub_segment.forEach((value) => params.append("sub_segment", value))
  }
  if (filters.clinic?.length) {
    filters.clinic.forEach((value) => params.append("clinic", value))
  }
  if (filters.doctor?.length) {
    filters.doctor.forEach((value) => params.append("doctor", value))
  }
  if (filters.service?.length) {
    filters.service.forEach((value) => params.append("service", value))
  }

  return params.toString()
}

export const useProfitAndLossRevenue = (filters: SummaryFilters = {}) => {
  const queryString = buildQueryString({
    currency: "SGD",
    year: 2025,
    last_n_years: 4,
    ...filters,
  })
  const url = `/summary/profit-and-loss/revenue/chart/?${queryString}`

  const snapshot = useSWRImmutable<CustomResponse<ProfitAndLossRevenueData[]>>(
    url,
    createFetcher
  )

  return { ...snapshot, data: snapshot.data?.data || [] }
}

export const useProfitAndLossEBITDA = (filters: SummaryFilters = {}) => {
  const queryString = buildQueryString({
    currency: "SGD",
    year: 2025,
    last_n_years: 4,
    ...filters,
  })
  const url = `/summary/profit-and-loss/ebitda/chart/?${queryString}`

  const snapshot = useSWRImmutable<CustomResponse<ProfitAndLossRevenueData[]>>(
    url,
    createFetcher
  )

  return { ...snapshot, data: snapshot.data?.data || [] }
}

export const useProfitAndLossNetProfit = (filters: SummaryFilters = {}) => {
  const queryString = buildQueryString({
    currency: "SGD",
    year: 2025,
    last_n_years: 4,
    ...filters,
  })
  const url = `/summary/profit-and-loss/net-profit/chart/?${queryString}`

  const snapshot = useSWRImmutable<CustomResponse<ProfitAndLossRevenueData[]>>(
    url,
    createFetcher
  )

  return { ...snapshot, data: snapshot.data?.data || [] }
}

export const useProfitAndLossRevenueBreakdown = ({
  type,
  filters = {},
}: {
  type: string
  filters?: SummaryFilters
}) => {
  const queryString = buildQueryString({
    currency: "SGD",
    year: 2025,
    last_n_years: 4,
    ...filters,
  })
  const url = `/summary/profit-and-loss/revenue-breakdown/${type}/chart/?${queryString}`

  const snapshot = useSWRImmutable<
    CustomResponse<ProfitAndLossRevenueBreakdownData[]>
  >(url, createFetcher)

  return { ...snapshot, data: snapshot.data?.data || [] }
}

export const useOperationsAndHRDoctors = (filters: SummaryFilters = {}) => {
  const queryString = buildQueryString({
    year: 2025,
    last_n_years: 4,
    ...filters,
  })
  const url = `/summary/operations-and-hr/doctors/chart/?${queryString}`

  const snapshot = useSWRImmutable<CustomResponse<StackedBarData[]>>(
    url,
    createFetcher
  )

  return { ...snapshot, data: snapshot.data?.data || [] }
}

export const useOperationsAndHRFTE = (filters: SummaryFilters = {}) => {
  const queryString = buildQueryString({
    year: 2025,
    last_n_years: 4,
    ...filters,
  })
  const url = `/summary/operations-and-hr/fte/chart/?${queryString}`

  const snapshot = useSWRImmutable<CustomResponse<StackedBarData[]>>(
    url,
    createFetcher
  )

  return { ...snapshot, data: snapshot.data?.data || [] }
}

export const useOperationsAndHROverview = (filters: SummaryFilters = {}) => {
  const queryString = buildQueryString({
    currency: "SGD",
    year: 2025,
    ...filters,
  })
  const url = `/summary/operations-and-hr/overview/?${queryString}`

  const snapshot = useSWRImmutable<CustomResponse<OperationsAndHROverviewData>>(
    url,
    createFetcher
  )

  return { ...snapshot, data: snapshot.data?.data }
}

export const useOperationsAndHROrgChart = () => {
  const url = "/summary/operations-and-hr/org-chart/"

  const snapshot = useSWRImmutable<CustomResponse<OrgChartData>>(
    url,
    createFetcher
  )

  return { ...snapshot, data: snapshot.data?.data || { name: "SMG" } }
}
