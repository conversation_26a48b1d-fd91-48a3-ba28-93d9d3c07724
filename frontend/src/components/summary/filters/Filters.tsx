"use client"

import { useState } from "react"
import {
  Calendar,
  Check,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
} from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

const COMPARISON_OPTIONS = ["Last 4 years", "No comparison"] as const
const TYPE_OPTIONS = ["Yearly", "Year YTD"] as const

const Filters = () => {
  const [selectedYear, setSelectedYear] = useState<number>(2025)
  const [selectedPeriodType, setSelectedPeriodType] = useState<
    "Year" | "Half-year" | "Quarter" | "Month"
  >("Year")
  const [selectedPeriod, setSelectedPeriod] = useState<string>("Year")

  const [selectedComparison, setSelectedComparison] = useState<
    (typeof COMPARISON_OPTIONS)[number]
  >(COMPARISON_OPTIONS[0])
  const [selectedType, setSelectedType] = useState<
    (typeof TYPE_OPTIONS)[number]
  >(TYPE_OPTIONS[0])

  const [showFilters, setShowFilters] = useState<boolean>(false)
  const [selectedFilters, setSelectedFilters] = useState<
    Record<string, string[]>
  >({})

  const handleFilterChange = (
    category: string,
    option: string,
    checked: boolean
  ) => {
    setSelectedFilters((prev) => {
      const newFilters = { ...prev }
      if (!newFilters[category]) {
        newFilters[category] = []
      }

      if (checked) {
        newFilters[category] = [...newFilters[category], option]
      } else {
        newFilters[category] = newFilters[category].filter(
          (item) => item !== option
        )
      }

      if (newFilters[category].length === 0) {
        delete newFilters[category]
      }

      return newFilters
    })
  }

  const getFilterSummary = () => {
    const filterEntries = Object.entries(selectedFilters)
    if (filterEntries.length === 0) return null

    return filterEntries
      .map(([category, options]) => `[${category}] ${options.join(", ")}`)
      .join(", ")
  }

  return (
    <>
      <div className="flex flex-wrap items-center gap-4">
        <div className="flex flex-wrap items-center gap-x-3 gap-y-2">
          <YearSelection
            selectedYear={selectedYear}
            setSelectedYear={setSelectedYear}
            selectedPeriod={selectedPeriod}
            setSelectedPeriod={setSelectedPeriod}
            selectedPeriodType={selectedPeriodType}
            setSelectedPeriodType={setSelectedPeriodType}
          />

          <p className="text-sm">Compared to</p>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="min-w-36 justify-between">
                {selectedComparison}
                <ChevronDown />
              </Button>
            </DropdownMenuTrigger>

            <DropdownMenuContent
              className="min-w-36"
              side="bottom"
              align="start"
              sideOffset={4}
            >
              {COMPARISON_OPTIONS.map((option, index) => (
                <DropdownMenuItem
                  key={option}
                  onClick={() => setSelectedComparison(option)}
                  className="justify-between"
                >
                  {index === 0 ? `Same period ${option.toLowerCase()}` : option}
                  {selectedComparison === option && <Check />}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="min-w-28 justify-between">
                {selectedType}
                <ChevronDown />
              </Button>
            </DropdownMenuTrigger>

            <DropdownMenuContent
              className="min-w-28"
              side="bottom"
              align="start"
              sideOffset={4}
            >
              {TYPE_OPTIONS.map((option) => (
                <DropdownMenuItem
                  key={option}
                  onClick={() => setSelectedType(option)}
                  className="justify-between"
                >
                  {option}
                  {selectedType === option && <Check />}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="flex items-center gap-3">
          <Button
            variant="link"
            onClick={() => setShowFilters(!showFilters)}
            className="text-muted-foreground hover:text-foreground h-auto min-w-14 p-0 underline"
          >
            {showFilters ? "See less" : "See more"}
          </Button>

          <Button
            variant="link"
            className="text-muted-foreground hover:text-foreground h-auto p-0 underline"
            onClick={() => {
              setSelectedYear(2025)
              setSelectedPeriodType("Year")
              setSelectedPeriod("Year")
              setSelectedComparison(COMPARISON_OPTIONS[0])
              setSelectedFilters({})
            }}
          >
            Reset
          </Button>
        </div>
      </div>

      {showFilters ? (
        <div className="bg-muted -mt-1 flex flex-wrap gap-2 self-start rounded-md p-2">
          <FilterCard
            title="Segment"
            options={["Women's Health", "Oncology", "Paediatric", "Imaging"]}
            selectedFilters={selectedFilters}
            handleFilterChange={handleFilterChange}
          />
          <FilterCard
            title="Sub-Segment"
            options={["IVF", "Obgyn", "Anti-aging", "Breast Surgery"]}
            selectedFilters={selectedFilters}
            handleFilterChange={handleFilterChange}
          />
          <FilterCard
            title="Clinic"
            options={["Astra", "TOGC", "WGC"]}
            selectedFilters={selectedFilters}
            handleFilterChange={handleFilterChange}
          />
          <FilterCard
            title="Doctor"
            options={["JL", "CC", "KL", "CK"]}
            selectedFilters={selectedFilters}
            handleFilterChange={handleFilterChange}
          />
          <FilterCard
            title="Service"
            options={["Drugs", "Procedure", "Consultation", "Consumables"]}
            selectedFilters={selectedFilters}
            handleFilterChange={handleFilterChange}
          />
        </div>
      ) : (
        getFilterSummary() && (
          <div className="-mt-1 text-sm">
            Other filters: {getFilterSummary()}
          </div>
        )
      )}
    </>
  )
}

export default Filters

const YearSelection = ({
  selectedYear,
  setSelectedYear,
  selectedPeriod,
  setSelectedPeriod,
  selectedPeriodType,
  setSelectedPeriodType,
}: {
  selectedYear: number
  setSelectedYear: (year: number) => void
  selectedPeriod: string
  setSelectedPeriod: (period: string) => void
  selectedPeriodType: "Year" | "Half-year" | "Quarter" | "Month"
  setSelectedPeriodType: (
    periodType: "Year" | "Half-year" | "Quarter" | "Month"
  ) => void
}) => (
  <DropdownMenu>
    <DropdownMenuTrigger asChild>
      <Button variant="outline" className="min-w-44 justify-between">
        <Calendar />
        {selectedPeriod} {selectedYear}
        <ChevronDown />
      </Button>
    </DropdownMenuTrigger>

    <DropdownMenuContent
      className="flex min-w-44 flex-col gap-2 p-4"
      side="bottom"
      align="start"
      sideOffset={4}
    >
      <div className="flex items-center justify-between">
        <Button
          variant="ghost"
          size="icon"
          className="size-6"
          onClick={() => setSelectedYear(selectedYear - 1)}
        >
          <ChevronLeft />
        </Button>

        <span className="font-medium">{selectedYear}</span>

        <Button
          variant="ghost"
          size="icon"
          className="size-6"
          onClick={() => setSelectedYear(selectedYear + 1)}
        >
          <ChevronRight />
        </Button>
      </div>

      <Tabs
        value={selectedPeriodType}
        onValueChange={(value) => {
          setSelectedPeriodType(value as typeof selectedPeriodType)

          if (value === "Year") setSelectedPeriod("Year")
          else if (value === "Half-year") setSelectedPeriod("H1")
          else if (value === "Quarter") setSelectedPeriod("Quarter 1")
          else if (value === "Month") setSelectedPeriod("January")
        }}
      >
        <TabsList className="w-full">
          {(["Year", "Half-year", "Quarter", "Month"] as const).map((type) => (
            <TabsTrigger key={type} value={type}>
              {type}
            </TabsTrigger>
          ))}
        </TabsList>

        <TabsContent value="Year">
          <Button
            size="sm"
            className="w-full text-xs"
            onClick={() => setSelectedPeriod("Year")}
          >
            Full Year
          </Button>
        </TabsContent>

        <TabsContent value="Half-year">
          <div className="grid grid-cols-2 gap-2">
            {["H1", "H2"].map((half) => (
              <Button
                key={half}
                variant={selectedPeriod === half ? "default" : "outline"}
                size="sm"
                className="text-xs"
                onClick={() => setSelectedPeriod(half)}
              >
                {half}
              </Button>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="Quarter">
          <div className="grid grid-cols-2 gap-2">
            {["Quarter 1", "Quarter 2", "Quarter 3", "Quarter 4"].map(
              (quarter) => (
                <Button
                  key={quarter}
                  variant={selectedPeriod === quarter ? "default" : "outline"}
                  size="sm"
                  className="text-xs"
                  onClick={() => setSelectedPeriod(quarter)}
                >
                  {quarter}
                </Button>
              )
            )}
          </div>
        </TabsContent>

        <TabsContent value="Month">
          <div className="grid grid-cols-2 gap-2">
            {[
              "January",
              "February",
              "March",
              "April",
              "May",
              "June",
              "July",
              "August",
              "September",
              "October",
              "November",
              "December",
            ].map((month) => (
              <Button
                key={month}
                variant={selectedPeriod === month ? "default" : "outline"}
                size="sm"
                className="text-xs"
                onClick={() => setSelectedPeriod(month)}
              >
                {month}
              </Button>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </DropdownMenuContent>
  </DropdownMenu>
)

const FilterCard = ({
  title,
  options,
  selectedFilters,
  handleFilterChange,
}: {
  title: string
  options: string[]
  selectedFilters: Record<string, string[]>
  handleFilterChange: (
    category: string,
    option: string,
    checked: boolean
  ) => void
}) => (
  <Card className="min-w-40 rounded-lg py-3 shadow-xs">
    <CardContent className="flex flex-col gap-3 px-3">
      <CardTitle>{title}</CardTitle>

      <div className="flex flex-col gap-2">
        {options.map((option) => (
          <Label key={option} className="cursor-pointer font-normal">
            <Checkbox
              checked={selectedFilters[title]?.includes(option) || false}
              onCheckedChange={(checked) =>
                handleFilterChange(title, option, checked as boolean)
              }
            />
            {option}
          </Label>
        ))}
      </div>
    </CardContent>
  </Card>
)
