"use client"

import React from "react"
import { Check, ChevronDown } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useSummaryFilters } from "@/contexts/summary-filters"

const OPTIONS = [
  { label: "Singapore", currency: "SGD" },
  { label: "Australia", currency: "AUD" },
] as const

const CountrySelector = () => {
  const { filters, updateFilters } = useSummaryFilters()

  const selectedOption =
    OPTIONS.find((option) => option.currency === filters.currency) || OPTIONS[0]

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="border-border! -my-0.5 h-auto min-w-32 justify-between rounded-none border-t-2 border-b-2 border-t-transparent! bg-transparent! p-0! text-2xl ring-0! outline-none!"
        >
          {selectedOption.label}
          <ChevronDown />
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent
        className="min-w-32"
        side="bottom"
        align="start"
        sideOffset={4}
      >
        {OPTIONS.map((option) => (
          <DropdownMenuItem
            key={option.currency}
            onClick={() => updateFilters({ currency: option.currency })}
            className="justify-between"
          >
            {option.label}
            {selectedOption.currency === option.currency && <Check />}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

export default CountrySelector
