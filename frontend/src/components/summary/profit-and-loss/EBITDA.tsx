"use client"

import React, { useState } from "react"

import { CHART_TYPES, ChartType } from "@/types/chart"
import { Card } from "@/components/CardComponents"
import { ChartTypeSelect } from "@/components/ChartComponents"
import { RevenueChart } from "@/components/summary/profit-and-loss/Revenue"
import { useSummaryFilters } from "@/contexts/summary-filters"
import { useProfitAndLossEBITDA } from "@/services/summary"

const EBITDA = () => {
  const { filters } = useSummaryFilters()
  const { data, isLoading } = useProfitAndLossEBITDA(filters)
  const [chartType, setChartType] = useState<ChartType>(CHART_TYPES[0])

  return (
    <Card
      title="EBITDA"
      flashNumberLabel="(excl. Vidaskin)"
      filters={
        <ChartTypeSelect
          value={chartType}
          setValue={setChartType}
          disabled={isLoading}
        />
      }
      isLoading={isLoading}
    >
      <RevenueChart data={data} chartType={chartType} />
    </Card>
  )
}

export default EBITDA
