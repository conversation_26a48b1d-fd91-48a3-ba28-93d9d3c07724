"use client"

import React from "react"
import dynamic from "next/dynamic"
import { <PERSON>, Line, <PERSON>C<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>Axi<PERSON> } from "recharts"

import { cn } from "@/lib/utils"
import { Card, CardContent, CardTitle } from "@/components/ui/card"

export const CustomPieChart = dynamic(() => Promise.resolve(_CustomPieChart), {
  ssr: false,
})
export const CustomLineChart = dynamic(
  () => Promise.resolve(_CustomLineChart),
  {
    ssr: false,
  }
)

const BusinessOverview = () => (
  <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-6">
    <OverviewCard
      title="Gross Revenue"
      value="$100M"
      amount={1.8}
      unit="%"
      topLeftItem={<CustomPieChart />}
      bottomRightItem={<CustomLineChart />}
    />

    <OverviewCard
      title="EBITDA"
      value="$30M"
      amount={-30}
      unit="%"
      topLeftItem={<CustomPieChart />}
      bottomRightItem={<CustomLineChart />}
    />

    <OverviewCard
      title="Bank Debt"
      value="$1M"
      amount={-20}
      unit="%"
      topLeftItem={
        <div className="mt-1 text-center">
          <p className="text-base font-bold text-nowrap text-red-600">2.0pts</p>
          <p className="text-[10px] leading-none text-nowrap">Debit Ratio</p>
        </div>
      }
      bottomRightItem={<CustomLineChart />}
    />

    <OverviewCard
      title="Cash Balance"
      value="$1M"
      amount={-20}
      unit="%"
      topLeftItem={
        <div className="mt-1 text-center">
          <p className="text-base font-bold text-nowrap text-red-600">2.0pts</p>
          <p className="text-[10px] leading-none text-nowrap">Debit Ratio</p>
        </div>
      }
      bottomRightItem={<CustomLineChart />}
    />

    <OverviewCard
      title="FTE"
      value="$1M"
      amount={-20}
      unit="%"
      topLeftItem={
        <div className="mt-1 text-center">
          <p className="text-base font-bold text-nowrap text-red-600">2.0pts</p>
          <p className="text-[10px] leading-none text-nowrap">Debit Ratio</p>
        </div>
      }
      bottomRightItem={<CustomLineChart />}
    />

    <OverviewCard
      title="FTE"
      value="$1M"
      amount={-20}
      unit="%"
      topLeftItem={
        <div className="mt-1 text-center">
          <p className="text-base font-bold text-nowrap text-red-600">2.0pts</p>
          <p className="text-[10px] leading-none text-nowrap">Debit Ratio</p>
        </div>
      }
      bottomRightItem={<CustomLineChart />}
    />

    {/* <OverviewCard
      title="EBITDA Margin"
      value="20%"
      amount={-5.4}
      unit="ppt"
      bottomRightItem={<CustomLineChart />}
    />

    <OverviewCard
      title="Bank Debit"
      value="$1M"
      amount={-20}
      unit="%"
      bottomRightItem={<CustomLineChart />}
    /> */}

    {/* <OverviewCard
      title="Cash Balance"
      value="$100M"
      amount={1.8}
      unit="%"
      topLeftItem={
        <div className="mt-1 text-center">
          <p className="text-base font-bold text-nowrap text-red-600">2.0pts</p>
          <p className="text-[10px] leading-none text-nowrap">Debit Ratio</p>
        </div>
      }
      bottomRightItem={<CustomLineChart />}
    /> */}

    {/* <OverviewCard
      title="FTE"
      value="300"
      amount={-30}
      unit="%"
      bottomRightItem={<CustomLineChart />}
    /> */}
  </div>
)

export default BusinessOverview

export const OverviewCard = ({
  title,
  value,
  amount,
  unit,
  topLeftItem,
  bottomRightItem,
}: {
  title: string
  value: string
  amount: number
  unit: string
  topLeftItem?: React.ReactNode
  bottomRightItem?: React.ReactNode
}) => (
  <Card className="rounded-lg py-3 shadow-xs">
    <CardContent className="flex flex-1 flex-col gap-2 px-3">
      <div className="flex h-4 items-center justify-between gap-2">
        <CardTitle className="text-muted-foreground text-sm leading-tight font-medium">
          {title}
        </CardTitle>

        {topLeftItem}
      </div>

      <p className="-mt-0.5 text-2xl font-bold">{value}</p>

      <div className="mt-auto flex items-end justify-between gap-2">
        <div
          className={cn(
            "rounded-full px-3 py-1.5 text-sm font-semibold",
            amount === 0 && "bg-muted text-muted-foreground",
            amount < 0 && "bg-red-100 text-red-600",
            amount > 0 && "bg-green-100 text-green-600"
          )}
        >
          {amount > 0 ? "+" : ""}
          {amount}
          {unit}
        </div>

        {bottomRightItem}
      </div>
    </CardContent>
  </Card>
)

const _CustomPieChart = () => {
  const data = [
    { name: "2023", value: 100, color: "var(--color-green-400)" },
    { name: "2024", value: 100, color: "var(--color-green-600)" },
    { name: "2025", value: 100, color: "var(--color-green-800)" },
  ]

  return (
    <div className="relative mt-1 shrink-0">
      <PieChart width={48} height={32}>
        <Pie
          data={data}
          dataKey="value"
          cx="50%"
          cy="100%"
          startAngle={180}
          endAngle={0}
          innerRadius={18}
          outerRadius={24}
          paddingAngle={0}
        >
          {data.map((data, index) => (
            <Cell key={`cell-${index}`} fill={data.color} />
          ))}
        </Pie>
      </PieChart>

      <div className="absolute inset-0 flex items-end justify-center pb-px">
        <span className="text-xs font-bold">90%</span>
      </div>
    </div>
  )
}

const _CustomLineChart = () => {
  const data = [
    {
      name: "2022",
      value: 1,
    },
    {
      name: "2023",
      value: 1.5,
    },
    {
      name: "2024",
      value: 1.5,
    },
    {
      name: "2025",
      value: 2,
    },
  ]

  return (
    <div className="-mt-3.5 flex flex-col items-center">
      <LineChart data={data} width={40} height={30}>
        <Line
          dataKey="value"
          stroke="var(--color-green-600)"
          strokeWidth={2}
          dot={false}
        />
        <YAxis hide domain={["dataMin", "dataMax"]} />
      </LineChart>

      <p className="text-center text-xs">Last 3yrs</p>
    </div>
  )
}
