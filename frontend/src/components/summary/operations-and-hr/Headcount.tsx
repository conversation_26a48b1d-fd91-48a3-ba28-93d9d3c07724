"use client"

import React from "react"
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  ResponsiveContainer,
  <PERSON>lt<PERSON>,
  <PERSON>Axis,
} from "recharts"

import { ChartType } from "@/types/chart"
import { StackedBarData } from "@/types/summary"
import { formatAbbreviatedCurrency } from "@/lib/number"
import { Card, CHART_HEIGHT } from "@/components/CardComponents"
import {
  CUSTOM_CHART_COLORS,
  WrappedTick,
  WrappedTooltip,
} from "@/components/ChartComponents"
import { useProfitAndLossRevenue } from "@/services/summary"

const Headcount = () => {
  const { isLoading } = useProfitAndLossRevenue()

  const data = [
    {
      name: "2021",
      "Women's Health": 35,
      Oncology: 20,
      Paediatric: 15,
      Imaging: 10,
      Others: 5,
    },
    {
      name: "2022",
      "Women's Health": 35,
      Oncology: 30,
      Paediatric: 10,
      Imaging: 30,
      Others: 5,
    },
    {
      name: "2023",
      "Women's Health": 35,
      Oncology: 30,
      Paediatric: 10,
      Imaging: 30,
      Others: 20,
    },
    {
      name: "2024",
      "Women's Health": 35,
      Oncology: 20,
      Paediatric: 15,
      Imaging: 10,
      Others: 10,
    },
    {
      name: "2025",
      "Women's Health": 35,
      Oncology: 20,
      Paediatric: 15,
      Imaging: 10,
      Others: 5,
    },
  ]

  return (
    <Card
      title="Headcount"
      tabs={["Full time", "Part time"]}
      flashNumberLabel="(excl. Vidaskin)"
      isLoading={isLoading}
    >
      <StackedBarChart data={data as unknown as StackedBarData[]} />
    </Card>
  )
}

export default Headcount

export const StackedBarChart = ({
  data,
  chartType = "Bar",
}: {
  data: StackedBarData[]
  chartType?: ChartType
}) => (
  <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
    {chartType === "Bar" ? (
      <BarChart data={data}>
        <XAxis
          dataKey="name"
          tick={WrappedTick}
          tickLine={false}
          interval={0}
        />

        <Tooltip
          content={<WrappedTooltip />}
          formatter={(value: number) => formatAbbreviatedCurrency(value, 0)}
        />

        <Legend
          iconType="circle"
          iconSize={8}
          verticalAlign="top"
          wrapperStyle={{ fontSize: 12, top: 0 }}
        />

        {Object.keys(data?.[0] || {})
          .filter((key) => key !== "name" && key !== "total")
          .map((key, index) => (
            <Bar
              key={key}
              dataKey={key}
              name={key}
              stackId="a"
              fill={
                index < 5
                  ? `var(--color-chart-${index + 1})`
                  : CUSTOM_CHART_COLORS[index - 5] || "var(--color-chart-1)"
              }
            >
              <LabelList
                dataKey={key}
                position="center"
                fill="white"
                formatter={(value: number) =>
                  formatAbbreviatedCurrency(value, 0)
                }
                fontSize={12}
              />
            </Bar>
          ))}
      </BarChart>
    ) : (
      <LineChart data={data} margin={{ top: 24, left: 24, right: 24 }}>
        <XAxis
          dataKey="name"
          tick={WrappedTick}
          tickLine={false}
          interval={0}
        />

        <Tooltip
          content={<WrappedTooltip />}
          formatter={(value: number) => formatAbbreviatedCurrency(value, 0)}
        />

        <Legend verticalAlign="top" wrapperStyle={{ fontSize: 12, top: 0 }} />

        {Object.keys(data?.[0] || {})
          .filter((key) => key !== "name" && key !== "total")
          .map((key, index) => (
            <Line
              key={key}
              type="monotone"
              dataKey={key}
              name={key}
              stroke={
                index < 5
                  ? `var(--color-chart-${index + 1})`
                  : CUSTOM_CHART_COLORS[index - 5] || "var(--color-chart-1)"
              }
              strokeWidth={2}
              dot={{ r: 3 }}
              activeDot={{ r: 5 }}
            >
              <LabelList
                dataKey={key}
                position="top"
                fill="black"
                formatter={(value: number) =>
                  formatAbbreviatedCurrency(value, 0)
                }
                fontSize={12}
              />
            </Line>
          ))}
      </LineChart>
    )}
  </ResponsiveContainer>
)
