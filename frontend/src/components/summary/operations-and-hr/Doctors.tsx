"use client"

import React, { useState } from "react"

import { CHART_TYPES, ChartType } from "@/types/chart"
import { StackedBarData } from "@/types/summary"
import { Card } from "@/components/CardComponents"
import { ChartTypeSelect, CustomSelect } from "@/components/ChartComponents"
import { StackedBarChart } from "@/components/summary/operations-and-hr/Headcount"
import { useSummaryFilters } from "@/contexts/summary-filters"
import { useOperationsAndHRDoctors } from "@/services/summary"

const OPTIONS = ["Default", "By Segment"]

const Doctors = () => {
  const { filters } = useSummaryFilters()
  const { data, isLoading } = useOperationsAndHRDoctors(filters)
  const [chartType, setChartType] = useState<ChartType>(CHART_TYPES[0])
  const [viewType, setViewType] = useState<string>(OPTIONS[0])

  return (
    <Card
      title="Doctors"
      flashNumberLabel="(excl. Vidaskin)"
      filters={
        <>
          <CustomSelect
            value={viewType}
            setValue={setViewType}
            options={OPTIONS}
            disabled={isLoading}
          />

          <ChartTypeSelect
            value={chartType}
            setValue={setChartType}
            disabled={isLoading}
          />
        </>
      }
      isLoading={isLoading}
    >
      <StackedBarChart
        data={data as unknown as StackedBarData[]}
        chartType={chartType}
      />
    </Card>
  )
}

export default Doctors
