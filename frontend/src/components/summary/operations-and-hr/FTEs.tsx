"use client"

import React, { useState } from "react"

import { StackedBarData } from "@/types/summary"
import { Card } from "@/components/CardComponents"
import { CustomSelect } from "@/components/ChartComponents"
import { StackedBarChart } from "@/components/summary/operations-and-hr/Headcount"
import { useSummaryFilters } from "@/contexts/summary-filters"
import { useOperationsAndHRFTE } from "@/services/summary"

const OPTIONS = ["Default", "By Staff Type"]

const FTEs = () => {
  const { filters } = useSummaryFilters()
  const { data, isLoading } = useOperationsAndHRFTE(filters)
  const [viewType, setViewType] = useState<string>(OPTIONS[0])

  return (
    <Card
      title="FTEs"
      flashNumberLabel="(excl. Vidaskin)"
      filters={
        <CustomSelect
          value={viewType}
          setValue={setViewType}
          options={OPTIONS}
          disabled={isLoading}
        />
      }
      isLoading={isLoading}
    >
      <StackedBarChart data={data as unknown as StackedBarData[]} />
    </Card>
  )
}

export default FTEs
