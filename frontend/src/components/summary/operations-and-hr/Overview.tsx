"use client"

import React from "react"
import { <PERSON>, LineChart, YA<PERSON><PERSON> } from "recharts"

import { ChartDataPoint } from "@/types/summary"
import { formatAbbreviatedCurrency, formatCurrency } from "@/lib/number"
import { Skeleton } from "@/components/ui/skeleton"
import { OverviewCard } from "@/components/summary/BusinessOverview"
import { useOperationsAndHROverview } from "@/services/summary"

const CustomLineChart = ({ data }: { data: ChartDataPoint[] }) => {
  return (
    <div className="-mt-3.5 flex flex-col items-center">
      <LineChart data={data} width={40} height={30}>
        <Line
          dataKey="value"
          stroke="var(--color-green-600)"
          strokeWidth={2}
          dot={false}
        />
        <YAxis hide domain={["dataMin", "dataMax"]} />
      </LineChart>

      <p className="text-center text-xs">Last 4yrs</p>
    </div>
  )
}

const Overview = () => {
  const { data, isLoading, error } = useOperationsAndHROverview()

  if (isLoading) {
    return (
      <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-4 xl:grid-cols-6">
        {Array.from({ length: 5 }).map((_, index) => (
          <Skeleton key={index} className="h-30 rounded-lg" />
        ))}
      </div>
    )
  }

  if (error || !data) {
    const emptyData = [
      { name: "2022", value: 0 },
      { name: "2023", value: 0 },
      { name: "2024", value: 0 },
      { name: "2025", value: 0 },
    ]

    return (
      <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-4 xl:grid-cols-6">
        <OverviewCard
          title="Revenue per FTE"
          value="$0"
          amount={0}
          unit="%"
          bottomRightItem={<CustomLineChart data={emptyData} />}
        />
        <OverviewCard
          title="Revenue per doctor"
          value="$0"
          amount={0}
          unit="%"
          bottomRightItem={<CustomLineChart data={emptyData} />}
        />
        <OverviewCard
          title="Employees"
          value="0"
          amount={0}
          unit="%"
          bottomRightItem={<CustomLineChart data={emptyData} />}
        />
        <OverviewCard
          title="Positive Attrition Rate"
          value="0%"
          amount={0}
          unit="%"
          bottomRightItem={<CustomLineChart data={emptyData} />}
        />
        <OverviewCard
          title="Negative Attrition Rate"
          value="0%"
          amount={0}
          unit="%"
          bottomRightItem={<CustomLineChart data={emptyData} />}
        />
      </div>
    )
  }

  return (
    <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-4 xl:grid-cols-6">
      <OverviewCard
        title="Revenue per FTE"
        value={`$${formatAbbreviatedCurrency(data.revenue_per_fte.value)}`}
        amount={data.revenue_per_fte.percentage}
        unit="%"
        bottomRightItem={
          <CustomLineChart data={data.revenue_per_fte.chart_data} />
        }
      />

      <OverviewCard
        title="Revenue per doctor"
        value={`$${formatAbbreviatedCurrency(data.revenue_per_doctor.value)}`}
        amount={data.revenue_per_doctor.percentage}
        unit="%"
        bottomRightItem={
          <CustomLineChart data={data.revenue_per_doctor.chart_data} />
        }
      />

      <OverviewCard
        title="Employees"
        value={formatCurrency(data.employees.value, 0)}
        amount={data.employees.percentage}
        unit="%"
        bottomRightItem={<CustomLineChart data={data.employees.chart_data} />}
      />

      <OverviewCard
        title="Positive Attrition Rate"
        value={`${data.positive_attrition_rate.value}%`}
        amount={data.positive_attrition_rate.percentage}
        unit="%"
        bottomRightItem={
          <CustomLineChart data={data.positive_attrition_rate.chart_data} />
        }
      />

      <OverviewCard
        title="Negative Attrition Rate"
        value={`${data.negative_attrition_rate.value}%`}
        amount={data.negative_attrition_rate.percentage}
        unit="%"
        bottomRightItem={
          <CustomLineChart data={data.negative_attrition_rate.chart_data} />
        }
      />
    </div>
  )
}

export default Overview
