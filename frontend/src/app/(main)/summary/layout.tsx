import React from "react"

import { Separator } from "@/components/ui/separator"
import { SidebarTrigger } from "@/components/ui/sidebar"
import CountrySelector from "@/components/summary/filters/CountrySelector"
import Filters from "@/components/summary/filters/Filters"
import { SummaryFiltersProvider } from "@/contexts/summary-filters"

const Layout = ({ children }: { children: React.ReactNode }) => (
  <SummaryFiltersProvider>
    <div className="flex flex-wrap items-center justify-between gap-x-2 gap-y-4">
      <div className="flex items-center gap-2">
        <SidebarTrigger className="-ml-1 hidden md:inline-flex" />
        <Separator
          orientation="vertical"
          className="mr-1 hidden data-[orientation=vertical]:h-6 md:block"
        />
        <h1 className="text-2xl font-medium">Summary:</h1>
        <CountrySelector />
      </div>

      <Filters />
    </div>

    {children}
  </SummaryFiltersProvider>
)

export default Layout
