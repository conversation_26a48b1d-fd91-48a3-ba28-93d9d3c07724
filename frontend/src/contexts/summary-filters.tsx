"use client"

import React, { createContext, useContext, useEffect, useState } from "react"
import { usePathname, useRouter, useSearchParams } from "next/navigation"

import { SummaryFilters } from "@/services/summary"

interface SummaryFiltersContextType {
  filters: SummaryFilters
  updateFilters: (newFilters: Partial<SummaryFilters>) => void
  resetFilters: () => void
  selectedYear: number
  setSelectedYear: (year: number) => void
  selectedComparison: "Last 4 years" | "No comparison"
  setSelectedComparison: (comparison: "Last 4 years" | "No comparison") => void
  selectedType: "Yearly" | "Year YTD"
  setSelectedType: (type: "Yearly" | "Year YTD") => void
  selectedFilters: Record<string, string[]>
  setSelectedFilters: React.Dispatch<
    React.SetStateAction<Record<string, string[]>>
  >
}

const SummaryFiltersContext = createContext<
  SummaryFiltersContextType | undefined
>(undefined)

// Default filter values
const DEFAULT_FILTERS: SummaryFilters = {
  currency: "SGD",
  year: 2025,
  last_n_years: 4,
}

// Helper function to parse URL search params to filters
const parseFiltersFromSearchParams = (
  searchParams: URLSearchParams
): SummaryFilters => {
  const filters: SummaryFilters = { ...DEFAULT_FILTERS }

  // Parse basic filters
  const currency = searchParams.get("currency")
  if (currency) filters.currency = currency

  const year = searchParams.get("year")
  if (year) filters.year = parseInt(year, 10)

  const lastNYears = searchParams.get("last_n_years")
  if (lastNYears) filters.last_n_years = parseInt(lastNYears, 10)

  // Parse array filters
  const segment = searchParams.getAll("segment")
  if (segment.length) filters.segment = segment

  const subSegment = searchParams.getAll("sub_segment")
  if (subSegment.length) filters.sub_segment = subSegment

  const clinic = searchParams.getAll("clinic")
  if (clinic.length) filters.clinic = clinic

  const doctor = searchParams.getAll("doctor")
  if (doctor.length) filters.doctor = doctor

  const service = searchParams.getAll("service")
  if (service.length) filters.service = service

  return filters
}

// Helper function to convert filters to URL search params
const filtersToSearchParams = (filters: SummaryFilters): URLSearchParams => {
  const params = new URLSearchParams()

  // Add basic filters (only if different from defaults)
  if (filters.currency && filters.currency !== DEFAULT_FILTERS.currency) {
    params.set("currency", filters.currency)
  }
  if (filters.year && filters.year !== DEFAULT_FILTERS.year) {
    params.set("year", filters.year.toString())
  }
  if (
    filters.last_n_years &&
    filters.last_n_years !== DEFAULT_FILTERS.last_n_years
  ) {
    params.set("last_n_years", filters.last_n_years.toString())
  }

  // Add array filters
  if (filters.segment?.length) {
    filters.segment.forEach((value) => params.append("segment", value))
  }
  if (filters.sub_segment?.length) {
    filters.sub_segment.forEach((value) => params.append("sub_segment", value))
  }
  if (filters.clinic?.length) {
    filters.clinic.forEach((value) => params.append("clinic", value))
  }
  if (filters.doctor?.length) {
    filters.doctor.forEach((value) => params.append("doctor", value))
  }
  if (filters.service?.length) {
    filters.service.forEach((value) => params.append("service", value))
  }

  return params
}

interface SummaryFiltersProviderProps {
  children: React.ReactNode
}

export const SummaryFiltersProvider: React.FC<SummaryFiltersProviderProps> = ({
  children,
}) => {
  const searchParams = useSearchParams()
  const router = useRouter()
  const pathname = usePathname()

  // Initialize filters from URL search params
  const [filters, setFilters] = useState<SummaryFilters>(() =>
    parseFiltersFromSearchParams(searchParams)
  )

  // UI state from the original Filters component
  const [selectedYear, setSelectedYear] = useState<number>(filters.year || 2025)
  const [selectedComparison, setSelectedComparison] = useState<
    "Last 4 years" | "No comparison"
  >("Last 4 years")
  const [selectedType, setSelectedType] = useState<"Yearly" | "Year YTD">(
    "Yearly"
  )
  const [selectedFilters, setSelectedFilters] = useState<
    Record<string, string[]>
  >(() => {
    const result: Record<string, string[]> = {}
    if (filters.segment?.length) result.Segment = filters.segment
    if (filters.sub_segment?.length) result["Sub-Segment"] = filters.sub_segment
    if (filters.clinic?.length) result.Clinic = filters.clinic
    if (filters.doctor?.length) result.Doctor = filters.doctor
    if (filters.service?.length) result.Service = filters.service
    return result
  })

  // Update filters and URL when filter state changes
  const updateFilters = (newFilters: Partial<SummaryFilters>) => {
    const updatedFilters = { ...filters, ...newFilters }
    setFilters(updatedFilters)

    // Update URL
    const params = filtersToSearchParams(updatedFilters)
    const newUrl = `${pathname}${params.toString() ? `?${params.toString()}` : ""}`
    router.replace(newUrl, { scroll: false })
  }

  // Reset filters to defaults
  const resetFilters = () => {
    setFilters(DEFAULT_FILTERS)
    setSelectedFilters({})
    router.replace(pathname, { scroll: false })
  }

  // Sync selectedYear with filters
  useEffect(() => {
    if (selectedYear !== filters.year) {
      updateFilters({ year: selectedYear })
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedYear])

  // Sync selectedFilters with filters
  useEffect(() => {
    const newFilters: Partial<SummaryFilters> = {}

    if (selectedFilters.Segment?.length) {
      newFilters.segment = selectedFilters.Segment
    } else {
      newFilters.segment = undefined
    }

    if (selectedFilters["Sub-Segment"]?.length) {
      newFilters.sub_segment = selectedFilters["Sub-Segment"]
    } else {
      newFilters.sub_segment = undefined
    }

    if (selectedFilters.Clinic?.length) {
      newFilters.clinic = selectedFilters.Clinic
    } else {
      newFilters.clinic = undefined
    }

    if (selectedFilters.Doctor?.length) {
      newFilters.doctor = selectedFilters.Doctor
    } else {
      newFilters.doctor = undefined
    }

    if (selectedFilters.Service?.length) {
      newFilters.service = selectedFilters.Service
    } else {
      newFilters.service = undefined
    }

    updateFilters(newFilters)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedFilters])

  // Update filters when URL search params change (e.g., browser back/forward)
  useEffect(() => {
    const newFilters = parseFiltersFromSearchParams(searchParams)
    setFilters(newFilters)
    setSelectedYear(newFilters.year || 2025)
  }, [searchParams])

  const contextValue: SummaryFiltersContextType = {
    filters,
    updateFilters,
    resetFilters,
    selectedYear,
    setSelectedYear,
    selectedComparison,
    setSelectedComparison,
    selectedType,
    setSelectedType,
    selectedFilters,
    setSelectedFilters,
  }

  return (
    <SummaryFiltersContext.Provider value={contextValue}>
      {children}
    </SummaryFiltersContext.Provider>
  )
}

export const useSummaryFilters = (): SummaryFiltersContextType => {
  const context = useContext(SummaryFiltersContext)

  if (!context) {
    throw new Error(
      "useSummaryFilters must be used within a SummaryFiltersProvider"
    )
  }

  return context
}
